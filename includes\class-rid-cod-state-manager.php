<?php
/**
 * State Management Class for RID COD Plugin
 * Handles CRUD operations for custom states/provinces
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class RID_COD_State_Manager {
    
    /**
     * Database table name for custom states
     */
    private static $table_name = 'rid_cod_custom_states';
    
    /**
     * Initialize the state manager
     */
    public static function init() {
        add_action('plugins_loaded', array(__CLASS__, 'create_table'));
    }
    
    /**
     * Create the custom states table
     */
    public static function create_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            country_code varchar(2) NOT NULL,
            state_code varchar(10) NOT NULL,
            state_name varchar(255) NOT NULL,
            is_custom tinyint(1) DEFAULT 1,
            sort_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_state (country_code, state_code),
            KEY country_code (country_code),
            KEY sort_order (sort_order)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // Update database version
        update_option('rid_cod_state_manager_db_version', '1.0');
    }
    
    /**
     * Get all states for a country (custom + default)
     *
     * @param string $country_code
     * @return array
     */
    public static function get_states_by_country($country_code) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        // Get custom states from database
        $custom_states = $wpdb->get_results($wpdb->prepare(
            "SELECT state_code, state_name FROM $table_name 
             WHERE country_code = %s 
             ORDER BY CAST(state_code AS UNSIGNED) ASC, state_code ASC",
            $country_code
        ), ARRAY_A);
        
        $states = array();
        
        // Add custom states
        if (!empty($custom_states)) {
            foreach ($custom_states as $state) {
                $states[$state['state_code']] = $state['state_name'];
            }
        }
        
        // If no custom states, fall back to default hardcoded states
        if (empty($states)) {
            $states = self::get_default_states_by_country($country_code);
        }
        
        return $states;
    }
    
    /**
     * Get default hardcoded states for a country
     *
     * @param string $country_code
     * @return array
     */
    private static function get_default_states_by_country($country_code) {
        // Use the existing Country Manager logic
        if (class_exists('RID_COD_Country_Manager')) {
            switch ($country_code) {
                case 'DZ':
                    require_once RID_COD_PLUGIN_DIR . 'includes/countries/algeria-states-cities.php';
                    return function_exists('rid_cod_get_algeria_states') ? rid_cod_get_algeria_states() : array();
                case 'MA':
                    require_once RID_COD_PLUGIN_DIR . 'includes/countries/morocco-states-cities.php';
                    return function_exists('rid_cod_get_morocco_states') ? rid_cod_get_morocco_states() : array();
                case 'SA':
                    require_once RID_COD_PLUGIN_DIR . 'includes/countries/saudi-states-cities.php';
                    return function_exists('rid_cod_get_saudi_states') ? rid_cod_get_saudi_states() : array();
                case 'TN':
                    require_once RID_COD_PLUGIN_DIR . 'includes/countries/tunisia-states-cities.php';
                    return function_exists('rid_cod_get_tunisia_states') ? rid_cod_get_tunisia_states() : array();
                case 'EG':
                    require_once RID_COD_PLUGIN_DIR . 'includes/countries/egypt-states-cities.php';
                    return function_exists('rid_cod_get_egypt_states') ? rid_cod_get_egypt_states() : array();
                case 'LY':
                    require_once RID_COD_PLUGIN_DIR . 'includes/countries/libya-states-cities.php';
                    return function_exists('rid_cod_get_libya_states') ? rid_cod_get_libya_states() : array();
                case 'JO':
                    require_once RID_COD_PLUGIN_DIR . 'includes/countries/jordan-states-cities.php';
                    return function_exists('rid_cod_get_jordan_states') ? rid_cod_get_jordan_states() : array();
                default:
                    return array();
            }
        }
        return array();
    }
    
    /**
     * Add a new custom state
     *
     * @param string $country_code
     * @param string $state_code
     * @param string $state_name
     * @return bool|WP_Error
     */
    public static function add_state($country_code, $state_code, $state_name) {
        global $wpdb;
        
        // Validate inputs
        $validation = self::validate_state_data($country_code, $state_code, $state_name);
        if (is_wp_error($validation)) {
            return $validation;
        }
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        // Check if state already exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE country_code = %s AND state_code = %s",
            $country_code, $state_code
        ));
        
        if ($existing) {
            return new WP_Error('state_exists', __('State code already exists for this country', 'rid-cod'));
        }
        
        // Insert new state
        $result = $wpdb->insert(
            $table_name,
            array(
                'country_code' => $country_code,
                'state_code' => $state_code,
                'state_name' => $state_name,
                'is_custom' => 1,
                'sort_order' => intval($state_code)
            ),
            array('%s', '%s', '%s', '%d', '%d')
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to add state to database', 'rid-cod'));
        }
        
        // Clear any cached state data
        self::clear_state_cache($country_code);
        
        return true;
    }
    
    /**
     * Update an existing state
     *
     * @param string $country_code
     * @param string $old_state_code
     * @param string $new_state_code
     * @param string $state_name
     * @return bool|WP_Error
     */
    public static function update_state($country_code, $old_state_code, $new_state_code, $state_name) {
        global $wpdb;
        
        // Validate inputs
        $validation = self::validate_state_data($country_code, $new_state_code, $state_name);
        if (is_wp_error($validation)) {
            return $validation;
        }
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        // Check if old state exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE country_code = %s AND state_code = %s",
            $country_code, $old_state_code
        ));
        
        if (!$existing) {
            return new WP_Error('state_not_found', __('State not found', 'rid-cod'));
        }
        
        // If state code changed, check if new code already exists
        if ($old_state_code !== $new_state_code) {
            $code_exists = $wpdb->get_var($wpdb->prepare(
                "SELECT id FROM $table_name WHERE country_code = %s AND state_code = %s AND id != %d",
                $country_code, $new_state_code, $existing
            ));
            
            if ($code_exists) {
                return new WP_Error('state_code_exists', __('New state code already exists', 'rid-cod'));
            }
        }
        
        // Update state
        $result = $wpdb->update(
            $table_name,
            array(
                'state_code' => $new_state_code,
                'state_name' => $state_name,
                'sort_order' => intval($new_state_code)
            ),
            array(
                'country_code' => $country_code,
                'state_code' => $old_state_code
            ),
            array('%s', '%s', '%d'),
            array('%s', '%s')
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to update state', 'rid-cod'));
        }
        
        // Update any existing shipping costs if state code changed
        if ($old_state_code !== $new_state_code) {
            self::update_shipping_costs_state_code($country_code, $old_state_code, $new_state_code);
        }
        
        // Clear any cached state data
        self::clear_state_cache($country_code);
        
        return true;
    }
    
    /**
     * Delete a custom state
     *
     * @param string $country_code
     * @param string $state_code
     * @return bool|WP_Error
     */
    public static function delete_state($country_code, $state_code) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::$table_name;
        
        // Check if state exists
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_name WHERE country_code = %s AND state_code = %s",
            $country_code, $state_code
        ));
        
        if (!$existing) {
            return new WP_Error('state_not_found', __('State not found', 'rid-cod'));
        }
        
        // Delete state
        $result = $wpdb->delete(
            $table_name,
            array(
                'country_code' => $country_code,
                'state_code' => $state_code
            ),
            array('%s', '%s')
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Failed to delete state', 'rid-cod'));
        }
        
        // Clear any cached state data
        self::clear_state_cache($country_code);

        return true;
    }

    /**
     * Restore default states for a country
     *
     * @param string $country_code
     * @return bool|WP_Error
     */
    public static function restore_default_states($country_code) {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        // Delete all custom states for this country
        $result = $wpdb->delete(
            $table_name,
            array('country_code' => $country_code),
            array('%s')
        );

        if ($result === false) {
            return new WP_Error('db_error', __('Failed to restore default states', 'rid-cod'));
        }

        // Clear any cached state data
        self::clear_state_cache($country_code);

        return true;
    }

    /**
     * Check if a country has custom states
     *
     * @param string $country_code
     * @return bool
     */
    public static function has_custom_states($country_code) {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE country_code = %s",
            $country_code
        ));

        return intval($count) > 0;
    }

    /**
     * Get a specific state by code
     *
     * @param string $country_code
     * @param string $state_code
     * @return array|null
     */
    public static function get_state($country_code, $state_code) {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        $state = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE country_code = %s AND state_code = %s",
            $country_code, $state_code
        ), ARRAY_A);

        return $state;
    }

    /**
     * Validate state data
     *
     * @param string $country_code
     * @param string $state_code
     * @param string $state_name
     * @return bool|WP_Error
     */
    private static function validate_state_data($country_code, $state_code, $state_name) {
        // Validate country code
        if (empty($country_code) || strlen($country_code) !== 2) {
            return new WP_Error('invalid_country', __('Invalid country code. Must be 2 characters.', 'rid-cod'));
        }

        // Validate state code
        if (empty($state_code)) {
            return new WP_Error('invalid_state_code', __('State code is required.', 'rid-cod'));
        }

        if (!is_numeric($state_code)) {
            return new WP_Error('invalid_state_code', __('State code must be numeric.', 'rid-cod'));
        }

        $state_code_int = intval($state_code);
        if ($state_code_int <= 0) {
            return new WP_Error('invalid_state_code', __('State code must be a positive number.', 'rid-cod'));
        }

        if ($state_code_int > 999) {
            return new WP_Error('invalid_state_code', __('State code cannot exceed 999.', 'rid-cod'));
        }

        // Validate state name
        $state_name = trim($state_name);
        if (empty($state_name)) {
            return new WP_Error('invalid_state_name', __('State name cannot be empty.', 'rid-cod'));
        }

        if (strlen($state_name) > 255) {
            return new WP_Error('invalid_state_name', __('State name cannot exceed 255 characters.', 'rid-cod'));
        }

        // Check for potentially harmful content
        if (preg_match('/[<>"\']/', $state_name)) {
            return new WP_Error('invalid_state_name', __('State name contains invalid characters.', 'rid-cod'));
        }

        return true;
    }

    /**
     * Update shipping costs when state code changes
     *
     * @param string $country_code
     * @param string $old_state_code
     * @param string $new_state_code
     */
    private static function update_shipping_costs_state_code($country_code, $old_state_code, $new_state_code) {
        $option_name = "rid_cod_shipping_costs_{$country_code}";
        $shipping_costs = get_option($option_name, array());

        if (isset($shipping_costs[$old_state_code])) {
            $shipping_costs[$new_state_code] = $shipping_costs[$old_state_code];
            unset($shipping_costs[$old_state_code]);
            update_option($option_name, $shipping_costs);
        }
    }

    /**
     * Clear state cache for a country
     *
     * @param string $country_code
     */
    private static function clear_state_cache($country_code) {
        // Clear any transients or cache related to states
        delete_transient("rid_cod_states_{$country_code}");
        delete_transient("rid_cod_state_costs_{$country_code}");

        // Clear WooCommerce cache if available
        if (function_exists('wc_delete_shop_order_transients')) {
            wc_delete_shop_order_transients();
        }
    }

    /**
     * Get the next available state code for a country
     *
     * @param string $country_code
     * @return string
     */
    public static function get_next_state_code($country_code) {
        global $wpdb;

        $table_name = $wpdb->prefix . self::$table_name;

        // Get the highest numeric state code
        $max_code = $wpdb->get_var($wpdb->prepare(
            "SELECT MAX(CAST(state_code AS UNSIGNED)) FROM $table_name WHERE country_code = %s",
            $country_code
        ));

        // If no custom states, check default states
        if (empty($max_code)) {
            $default_states = self::get_default_states_by_country($country_code);
            if (!empty($default_states)) {
                $codes = array_keys($default_states);
                $numeric_codes = array_filter($codes, 'is_numeric');
                if (!empty($numeric_codes)) {
                    $max_code = max($numeric_codes);
                }
            }
        }

        $next_code = intval($max_code) + 1;
        return sprintf('%02d', $next_code);
    }

    /**
     * Import default states to custom states table
     *
     * @param string $country_code
     * @return bool|WP_Error
     */
    public static function import_default_states($country_code) {
        global $wpdb;

        $default_states = self::get_default_states_by_country($country_code);

        if (empty($default_states)) {
            return new WP_Error('no_default_states', __('No default states found for this country', 'rid-cod'));
        }

        $table_name = $wpdb->prefix . self::$table_name;

        // Clear existing custom states
        $wpdb->delete($table_name, array('country_code' => $country_code), array('%s'));

        // Insert default states as custom states
        foreach ($default_states as $state_code => $state_name) {
            $wpdb->insert(
                $table_name,
                array(
                    'country_code' => $country_code,
                    'state_code' => $state_code,
                    'state_name' => $state_name,
                    'is_custom' => 0, // Mark as imported from defaults
                    'sort_order' => intval($state_code)
                ),
                array('%s', '%s', '%s', '%d', '%d')
            );
        }

        // Clear cache
        self::clear_state_cache($country_code);

        return true;
    }
}
