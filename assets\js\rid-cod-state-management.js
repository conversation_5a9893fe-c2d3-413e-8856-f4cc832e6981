/**
 * State Management JavaScript for RID COD Plugin
 * Handles add, edit, delete, and restore operations for states/provinces
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // State management variables
    var modal = $('#state-management-modal');
    var form = $('#state-management-form');
    var messageDiv = $('#state-form-message');
    var tableBody = $('#states-table-body');
    var currentCountry = $('#rid_cod_selected_country').val() || 'DZ';
    
    // Create nonce for AJAX requests
    var stateNonce = (typeof ridCodStateManagement !== 'undefined') ? ridCodStateManagement.nonce : '';
    
    // Modal controls
    var addBtn = $('#add-state-btn');
    var importBtn = $('#import-default-states-btn');
    var restoreBtn = $('#restore-default-states-btn');
    var saveBtn = $('#save-state-btn');
    var cancelBtn = $('#cancel-state-btn');
    var closeBtn = $('.state-modal-close');
    
    /**
     * Initialize state management
     */
    function init() {
        bindEvents();
        updateCountryContext();
    }
    
    /**
     * Bind event handlers
     */
    function bindEvents() {
        // Modal controls
        addBtn.on('click', openAddModal);
        importBtn.on('click', importDefaultStates);
        restoreBtn.on('click', restoreDefaultStates);
        saveBtn.on('click', saveState);
        cancelBtn.on('click', closeModal);
        closeBtn.on('click', closeModal);
        
        // Table controls
        $(document).on('click', '.edit-state-btn', openEditModal);
        $(document).on('click', '.delete-state-btn', deleteState);
        
        // Modal background click
        modal.on('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
        
        // Form validation
        $('#state-code, #state-name').on('input', validateForm);
        
        // Country change handler
        $('#rid_cod_selected_country').on('change', function() {
            currentCountry = $(this).val();
            updateCountryContext();
        });
        
        // Escape key to close modal
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && modal.is(':visible')) {
                closeModal();
            }
        });
    }
    
    /**
     * Update country context
     */
    function updateCountryContext() {
        currentCountry = $('#rid_cod_selected_country').val() || 'DZ';
    }
    
    /**
     * Open add state modal
     */
    function openAddModal() {
        resetForm();
        $('#state-modal-title').text('Add State/Province');
        $('#state-action').val('add');
        $('#original-state-code').val('');
        
        // Get next available state code
        getNextStateCode();
        
        modal.show();
        $('#state-code').focus();
    }
    
    /**
     * Open edit state modal
     */
    function openEditModal() {
        var btn = $(this);
        var stateCode = btn.data('state-code');
        var stateName = btn.data('state-name');
        
        resetForm();
        $('#state-modal-title').text('Edit State/Province');
        $('#state-action').val('edit');
        $('#original-state-code').val(stateCode);
        $('#state-code').val(stateCode);
        $('#state-name').val(stateName);
        
        modal.show();
        $('#state-name').focus();
    }
    
    /**
     * Close modal
     */
    function closeModal() {
        modal.hide();
        resetForm();
    }
    
    /**
     * Reset form
     */
    function resetForm() {
        form[0].reset();
        messageDiv.hide().removeClass('notice-success notice-error');
        $('#state-action').val('add');
        $('#original-state-code').val('');
    }
    
    /**
     * Validate form
     */
    function validateForm() {
        var stateCode = $('#state-code').val().trim();
        var stateName = $('#state-name').val().trim();
        var isValid = true;
        var errors = [];

        // Validate state code
        if (!stateCode) {
            errors.push('State code is required');
            isValid = false;
        } else if (!$.isNumeric(stateCode)) {
            errors.push('State code must be numeric');
            isValid = false;
        } else if (parseInt(stateCode) <= 0) {
            errors.push('State code must be a positive number');
            isValid = false;
        } else if (parseInt(stateCode) > 999) {
            errors.push('State code cannot exceed 999');
            isValid = false;
        }

        // Validate state name
        if (!stateName) {
            errors.push('State name is required');
            isValid = false;
        } else if (stateName.length > 255) {
            errors.push('State name cannot exceed 255 characters');
            isValid = false;
        } else if (/[<>"']/.test(stateName)) {
            errors.push('State name contains invalid characters');
            isValid = false;
        }

        // Show validation errors
        if (errors.length > 0 && !isValid) {
            showMessage(errors.join('<br>'), 'error');
        } else {
            messageDiv.hide();
        }

        saveBtn.prop('disabled', !isValid);
        return isValid;
    }
    
    /**
     * Get next available state code
     */
    function getNextStateCode() {
        var maxCode = 0;
        tableBody.find('tr').each(function() {
            var code = parseInt($(this).data('state-code'));
            if (code > maxCode) {
                maxCode = code;
            }
        });
        
        var nextCode = maxCode + 1;
        $('#state-code').val(String(nextCode).padStart(2, '0'));
    }
    
    /**
     * Save state (add or edit)
     */
    function saveState() {
        if (!validateForm()) {
            showMessage('Please fill in all required fields correctly.', 'error');
            return;
        }
        
        var action = $('#state-action').val();
        var stateCode = $('#state-code').val().trim();
        var stateName = $('#state-name').val().trim();
        var originalCode = $('#original-state-code').val();
        
        // Format state code
        stateCode = String(stateCode).padStart(2, '0');
        
        var ajaxAction = action === 'edit' ? 'rid_cod_edit_state' : 'rid_cod_add_state';
        var data = {
            action: ajaxAction,
            nonce: stateNonce,
            country_code: currentCountry,
            state_code: stateCode,
            state_name: stateName
        };
        
        if (action === 'edit') {
            data.old_state_code = originalCode;
            data.new_state_code = stateCode;
        }
        
        setLoading(true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    showMessage(response.data.message, 'success');
                    
                    if (action === 'add') {
                        addStateToTable(stateCode, stateName);
                    } else {
                        updateStateInTable(originalCode, stateCode, stateName);
                    }
                    
                    setTimeout(function() {
                        closeModal();
                        // Refresh the page to update delivery pricing section
                        location.reload();
                    }, 1500);
                } else {
                    showMessage(response.data.message || 'An error occurred.', 'error');
                }
            },
            error: function() {
                showMessage('Network error. Please try again.', 'error');
            },
            complete: function() {
                setLoading(false);
            }
        });
    }
    
    /**
     * Delete state
     */
    function deleteState() {
        var btn = $(this);
        var stateCode = btn.data('state-code');
        var stateName = btn.data('state-name');

        var confirmMessage = 'Are you sure you want to delete "' + stateName + '"?\n\n' +
                           'This will also remove any delivery pricing data for this state.\n\n' +
                           'This action cannot be undone.';

        if (!confirm(confirmMessage)) {
            return;
        }
        
        var data = {
            action: 'rid_cod_delete_state',
            nonce: stateNonce,
            country_code: currentCountry,
            state_code: stateCode
        };
        
        setTableLoading(true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    removeStateFromTable(stateCode);
                    showNotification(response.data.message, 'success');
                    
                    // Refresh the page to update delivery pricing section
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showNotification(response.data.message || 'Failed to delete state.', 'error');
                }
            },
            error: function() {
                showNotification('Network error. Please try again.', 'error');
            },
            complete: function() {
                setTableLoading(false);
            }
        });
    }
    
    /**
     * Import default states
     */
    function importDefaultStates() {
        if (!confirm('This will import the default states for the selected country.\n\nAny existing custom states will be replaced. Continue?')) {
            return;
        }
        
        var data = {
            action: 'rid_cod_import_default_states',
            nonce: stateNonce,
            country_code: currentCountry
        };
        
        setTableLoading(true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showNotification(response.data.message || 'Failed to import default states.', 'error');
                }
            },
            error: function() {
                showNotification('Network error. Please try again.', 'error');
            },
            complete: function() {
                setTableLoading(false);
            }
        });
    }
    
    /**
     * Restore default states
     */
    function restoreDefaultStates() {
        if (!confirm('This will restore the original default states for the selected country.\n\nAll custom states and their delivery pricing will be permanently deleted. Continue?')) {
            return;
        }
        
        var data = {
            action: 'rid_cod_restore_default_states',
            nonce: stateNonce,
            country_code: currentCountry
        };
        
        setTableLoading(true);
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    showNotification(response.data.message, 'success');
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showNotification(response.data.message || 'Failed to restore default states.', 'error');
                }
            },
            error: function() {
                showNotification('Network error. Please try again.', 'error');
            },
            complete: function() {
                setTableLoading(false);
            }
        });
    }

    /**
     * Add state to table
     */
    function addStateToTable(stateCode, stateName) {
        var row = $('<tr data-state-code="' + stateCode + '">');
        row.append('<td><strong>' + stateCode + '</strong></td>');
        row.append('<td>' + stateName + '</td>');
        row.append('<td>' +
            '<button type="button" class="button button-small edit-state-btn" ' +
            'data-state-code="' + stateCode + '" data-state-name="' + stateName + '">Edit</button> ' +
            '<button type="button" class="button button-small button-link-delete delete-state-btn" ' +
            'data-state-code="' + stateCode + '" data-state-name="' + stateName + '">Delete</button>' +
            '</td>');

        // Insert in correct position (sorted by code)
        var inserted = false;
        tableBody.find('tr').each(function() {
            var existingCode = parseInt($(this).data('state-code'));
            if (parseInt(stateCode) < existingCode) {
                $(this).before(row);
                inserted = true;
                return false;
            }
        });

        if (!inserted) {
            tableBody.append(row);
        }

        // Highlight new row
        row.addClass('highlight');
        setTimeout(function() {
            row.removeClass('highlight');
        }, 2000);
    }

    /**
     * Update state in table
     */
    function updateStateInTable(oldCode, newCode, stateName) {
        var row = tableBody.find('tr[data-state-code="' + oldCode + '"]');

        if (row.length) {
            // Update row data
            row.attr('data-state-code', newCode);
            row.find('td:first').html('<strong>' + newCode + '</strong>');
            row.find('td:nth-child(2)').text(stateName);

            // Update button data
            row.find('.edit-state-btn, .delete-state-btn')
                .attr('data-state-code', newCode)
                .attr('data-state-name', stateName);

            // If code changed, re-sort table
            if (oldCode !== newCode) {
                row.remove();
                addStateToTable(newCode, stateName);
            } else {
                // Highlight updated row
                row.addClass('highlight');
                setTimeout(function() {
                    row.removeClass('highlight');
                }, 2000);
            }
        }
    }

    /**
     * Remove state from table
     */
    function removeStateFromTable(stateCode) {
        var row = tableBody.find('tr[data-state-code="' + stateCode + '"]');
        if (row.length) {
            row.fadeOut(300, function() {
                $(this).remove();
            });
        }
    }

    /**
     * Show message in modal
     */
    function showMessage(message, type) {
        messageDiv.removeClass('notice-success notice-error')
                  .addClass('notice-' + type)
                  .html('<p>' + message + '</p>')
                  .show();
    }

    /**
     * Show notification (outside modal)
     */
    function showNotification(message, type) {
        var notification = $('<div class="notice notice-' + type + ' is-dismissible">' +
            '<p>' + message + '</p>' +
            '<button type="button" class="notice-dismiss">' +
            '<span class="screen-reader-text">Dismiss this notice.</span>' +
            '</button>' +
            '</div>');

        $('.state-management-section').prepend(notification);

        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);

        // Manual dismiss
        notification.find('.notice-dismiss').on('click', function() {
            notification.fadeOut(300, function() {
                $(this).remove();
            });
        });
    }

    /**
     * Set loading state for modal
     */
    function setLoading(loading) {
        if (loading) {
            modal.addClass('state-loading');
            saveBtn.prop('disabled', true).text('Saving...');
        } else {
            modal.removeClass('state-loading');
            saveBtn.prop('disabled', false).text('Save');
        }
    }

    /**
     * Set loading state for table
     */
    function setTableLoading(loading) {
        var container = $('.states-table-container');
        if (loading) {
            container.addClass('state-loading');
            $('.state-management-controls button').prop('disabled', true);
        } else {
            container.removeClass('state-loading');
            $('.state-management-controls button').prop('disabled', false);
        }
    }

    /**
     * Get WordPress nonce for state management
     */
    function getStateNonce() {
        // Get nonce from localized script data
        if (typeof ridCodStateManagement !== 'undefined' && ridCodStateManagement.nonce) {
            return ridCodStateManagement.nonce;
        }

        // Fallback: empty string
        return '';
    }

    // Initialize when document is ready
    init();

    // Set nonce
    stateNonce = getStateNonce();

    // Add CSS for highlight effect
    $('<style>')
        .prop('type', 'text/css')
        .html('.highlight { background-color: #fff3cd !important; transition: background-color 2s ease; }')
        .appendTo('head');
});
