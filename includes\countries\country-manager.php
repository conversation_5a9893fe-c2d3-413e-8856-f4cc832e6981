<?php
/**
 * Country Manager Class
 * Manages multiple countries support for RID COD plugin
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class RID_COD_Country_Manager {
    
    /**
     * Get supported countries
     * 
     * Phone validation rules updated with correct patterns:
     * - Algeria (DZ): Starts with 05, 06, or 07 + 8 more digits (10 total)
     * - Morocco (MA): Starts with 0 + 9 more digits (10 total)
     * - Saudi Arabia (SA): Starts with 05 + 8 more digits (10 total)
     * - Tunisia (TN): Starts with 2, 4, 5, or 9 + 7 more digits (8 total)
     * - Egypt (EG): Starts with 010, 011, 012, or 015 + 8 more digits (11 total)
     * - Libya (LY): Starts with 091, 092, 093, 094, or 095 + 7 more digits (10 total)
     * - Jordan (JO): Starts with 075, 077, 078, or 079 + 7 more digits (10 total)
     *
     * @return array
     */
    public static function get_supported_countries() {
        return array(
            'DZ' => array(
                'name' => 'الجزائر',
                'name_en' => 'Algeria',
                'currency' => 'DZD',
                'currency_symbol' => 'د.ج',
                'phone_pattern' => '/^0[5-7]\d{8}$/',
                'phone_example' => '0551234567 أو 0661234567 أو 0771234567'
            ),
            'MA' => array(
                'name' => 'المغرب',
                'name_en' => 'Morocco',
                'currency' => 'MAD',
                'currency_symbol' => 'د.م',
                'phone_pattern' => '/^0\d{9}$/',
                'phone_example' => '0661234567 أو 0551234567 أو 0771234567'
            ),
            'SA' => array(
                'name' => 'السعودية',
                'name_en' => 'Saudi Arabia',
                'currency' => 'SAR',
                'currency_symbol' => 'ر.س',
                'phone_pattern' => '/^05\d{8}$/',
                'phone_example' => '0501234567 أو 0551234567'
            ),
            'TN' => array(
                'name' => 'تونس',
                'name_en' => 'Tunisia',
                'currency' => 'TND',
                'currency_symbol' => 'د.ت',
                'phone_pattern' => '/^[2459]\d{7}$/',
                'phone_example' => '22123456 أو 52123456 أو 92123456'
            ),
            'EG' => array(
                'name' => 'مصر',
                'name_en' => 'Egypt',
                'currency' => 'EGP',
                'currency_symbol' => 'ج.م',
                'phone_pattern' => '/^01[0125]\d{8}$/',
                'phone_example' => '01012345678 أو 01112345678'
            ),
            'LY' => array(
                'name' => 'ليبيا',
                'name_en' => 'Libya',
                'currency' => 'LYD',
                'currency_symbol' => 'د.ل',
                'phone_pattern' => '/^0(?:91|92|93|94|95)\d{7}$/',
                'phone_example' => '0912345678 أو 0921234567'
            ),
            'JO' => array(
                'name' => 'الأردن',
                'name_en' => 'Jordan',
                'currency' => 'JOD',
                'currency_symbol' => 'د.أ',
                'phone_pattern' => '/^07(?:5|7|8|9)\d{7}$/',
                'phone_example' => '0791234567 أو 0771234567'
            )
        );
    }

    /**
     * Get country data by code
     *
     * @param string $country_code
     * @return array|false
     */
    public static function get_country_data($country_code) {
        $countries = self::get_supported_countries();
        return isset($countries[$country_code]) ? $countries[$country_code] : false;
    }

    /**
     * Get states by country
     *
     * @param string $country_code
     * @return array
     */
    public static function get_states_by_country($country_code) {
        // Check if State Manager is available and use it first
        if (class_exists('RID_COD_State_Manager')) {
            return RID_COD_State_Manager::get_states_by_country($country_code);
        }

        // Fallback to hardcoded states
        switch ($country_code) {
            case 'DZ':
                return self::get_algeria_states();
            case 'MA':
                return self::get_morocco_states();
            case 'SA':
                return self::get_saudi_states();
            case 'TN':
                return self::get_tunisia_states();
            case 'EG':
                return self::get_egypt_states();
            case 'LY':
                return self::get_libya_states();
            case 'JO':
                return self::get_jordan_states();
            default:
                return array();
        }
    }

    /**
     * Get cities by state and country
     *
     * @param string $country_code
     * @param string $state_code
     * @return array
     */
    public static function get_cities_by_state($country_code, $state_code = '') {
        switch ($country_code) {
            case 'DZ':
                return self::get_algeria_cities_by_state($state_code);
            case 'MA':
                return self::get_morocco_cities_by_state($state_code);
            case 'SA':
                return self::get_saudi_cities_by_state($state_code);
            case 'TN':
                return self::get_tunisia_cities_by_state($state_code);
            case 'EG':
                return self::get_egypt_cities_by_state($state_code);
            case 'LY':
                return self::get_libya_cities_by_state($state_code);
            case 'JO':
                return self::get_jordan_cities_by_state($state_code);
            default:
                return array();
        }
    }

    /**
     * Validate phone number for specific country
     * Simplified validation: only checks prefix and total digit count
     *
     * @param string $phone
     * @param string $country_code
     * @return bool
     */
    public static function validate_phone($phone, $country_code) {
        $country_data = self::get_country_data($country_code);
        if (!$country_data) {
            return false;
        }

        // Remove all non-numeric characters (spaces, dashes, parentheses, etc.)
        $phone = preg_replace('/[^\d]/', '', $phone);
        
        // Remove leading + or 00 if present
        $phone = preg_replace('/^(\+|00)/', '', $phone);
        
        // Remove country code if present at the beginning
        switch ($country_code) {
            case 'DZ':
                $phone = preg_replace('/^213/', '', $phone); // Remove Algeria country code
                break;
            case 'MA':
                $phone = preg_replace('/^212/', '', $phone); // Remove Morocco country code
                break;
            case 'SA':
                $phone = preg_replace('/^966/', '', $phone); // Remove Saudi Arabia country code
                break;
            case 'TN':
                $phone = preg_replace('/^216/', '', $phone); // Remove Tunisia country code
                break;
            case 'EG':
                $phone = preg_replace('/^20/', '', $phone); // Remove Egypt country code
                break;
            case 'LY':
                $phone = preg_replace('/^218/', '', $phone); // Remove Libya country code
                break;
            case 'JO':
                $phone = preg_replace('/^962/', '', $phone); // Remove Jordan country code
                break;
        }
        
        // Use the regex pattern for validation
        return preg_match($country_data['phone_pattern'], $phone);
    }

    /**
     * Get phone validation pattern for country
     *
     * @param string $country_code
     * @return string
     */
    public static function get_phone_pattern($country_code) {
        $country_data = self::get_country_data($country_code);
        return $country_data ? $country_data['phone_pattern'] : '';
    }

    /**
     * Get phone example for country
     *
     * @param string $country_code
     * @return string
     */
    public static function get_phone_example($country_code) {
        $country_data = self::get_country_data($country_code);
        return $country_data ? $country_data['phone_example'] : '';
    }

    /**
     * Format price with selected country's currency
     *
     * @param float $price
     * @param string $country_code Optional. If not provided, uses current country setting
     * @return string
     */
    public static function format_price_with_currency($price, $country_code = null) {
        if (!$country_code) {
            $country_code = self::get_current_country();
        }
        
        $country_data = self::get_country_data($country_code);
        $currency_symbol = $country_data ? $country_data['currency_symbol'] : 'د.ج';
        
        $formatted_price = number_format($price, 2, ',', '.');
        
        return $currency_symbol . ' ' . $formatted_price;
    }

    /**
     * Get Algeria states (existing function)
     */
    private static function get_algeria_states() {
        // Include existing Algeria data
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/algeria-states-cities.php';
        return function_exists('rid_cod_get_algeria_states') ? rid_cod_get_algeria_states() : array();
    }

    /**
     * Get Algeria cities by state (existing function)
     */
    private static function get_algeria_cities_by_state($state_code = '') {
        // Include existing Algeria data
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/algeria-states-cities.php';
        return function_exists('rid_cod_get_cities_by_state') ? rid_cod_get_cities_by_state($state_code) : array();
    }

    /**
     * Get Morocco states
     */
    private static function get_morocco_states() {
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/morocco-states-cities.php';
        return function_exists('rid_cod_get_morocco_states') ? rid_cod_get_morocco_states() : array();
    }

    /**
     * Get Morocco cities by state
     */
    private static function get_morocco_cities_by_state($state_code = '') {
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/morocco-states-cities.php';
        return function_exists('rid_cod_get_morocco_cities_by_state') ? rid_cod_get_morocco_cities_by_state($state_code) : array();
    }

    /**
     * Get Saudi states
     */
    private static function get_saudi_states() {
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/saudi-states-cities.php';
        return function_exists('rid_cod_get_saudi_states') ? rid_cod_get_saudi_states() : array();
    }

    /**
     * Get Saudi cities by state
     */
    private static function get_saudi_cities_by_state($state_code = '') {
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/saudi-states-cities.php';
        return function_exists('rid_cod_get_saudi_cities_by_state') ? rid_cod_get_saudi_cities_by_state($state_code) : array();
    }

    /**
     * Get Tunisia states
     */
    private static function get_tunisia_states() {
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/tunisia-states-cities.php';
        return function_exists('rid_cod_get_tunisia_states') ? rid_cod_get_tunisia_states() : array();
    }

    /**
     * Get Tunisia cities by state
     */
    private static function get_tunisia_cities_by_state($state_code = '') {
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/tunisia-states-cities.php';
        return function_exists('rid_cod_get_tunisia_cities_by_state') ? rid_cod_get_tunisia_cities_by_state($state_code) : array();
    }

    /**
     * Get Egypt states
     */
    private static function get_egypt_states() {
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/egypt-states-cities.php';
        return function_exists('rid_cod_get_egypt_states') ? rid_cod_get_egypt_states() : array();
    }

    /**
     * Get Egypt cities by state
     */
    private static function get_egypt_cities_by_state($state_code = '') {
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/egypt-states-cities.php';
        return function_exists('rid_cod_get_egypt_cities_by_state') ? rid_cod_get_egypt_cities_by_state($state_code) : array();
    }

    /**
     * Get Libya states
     */
    private static function get_libya_states() {
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/libya-states-cities.php';
        return function_exists('rid_cod_get_libya_states') ? rid_cod_get_libya_states() : array();
    }

    /**
     * Get Libya cities by state
     */
    private static function get_libya_cities_by_state($state_code = '') {
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/libya-states-cities.php';
        return function_exists('rid_cod_get_libya_cities_by_state') ? rid_cod_get_libya_cities_by_state($state_code) : array();
    }

    /**
     * Get Jordan states
     */
    private static function get_jordan_states() {
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/jordan-states-cities.php';
        return function_exists('rid_cod_get_jordan_states') ? rid_cod_get_jordan_states() : array();
    }

    /**
     * Get Jordan cities by state
     */
    private static function get_jordan_cities_by_state($state_code = '') {
        require_once RID_COD_PLUGIN_DIR . 'includes/countries/jordan-states-cities.php';
        return function_exists('rid_cod_get_jordan_cities_by_state') ? rid_cod_get_jordan_cities_by_state($state_code) : array();
    }

    /**
     * Get current selected country from settings
     *
     * @return string
     */
    public static function get_current_country() {
        return get_option('rid_cod_selected_country', 'DZ'); // Default to Algeria
    }

    /**
     * Set current country in settings
     *
     * @param string $country_code
     * @return bool
     */
    public static function set_current_country($country_code) {
        $countries = self::get_supported_countries();
        if (isset($countries[$country_code])) {
            return update_option('rid_cod_selected_country', $country_code);
        }
        return false;
    }

    /**
     * Get all countries data for JavaScript
     *
     * @return array
     */
    public function get_all_countries_data() {
        $countries_data = array();
        $countries = self::get_supported_countries();
        
        foreach ($countries as $code => $country_info) {
            $countries_data[$code] = array(
                'name' => $country_info['name'],
                'name_en' => $country_info['name_en'],
                'currency' => $country_info['currency'],
                'currency_symbol' => $country_info['currency_symbol'],
                'states' => self::get_states_by_country($code),
                'phone_pattern' => self::get_phone_pattern($code),
                'phone_example' => self::get_phone_example($code)
            );
        }
        
        return $countries_data;
    }

    /**
     * Get all phone patterns for JavaScript
     *
     * @return array
     */
    public function get_all_phone_patterns() {
        $patterns = array();
        $countries = self::get_supported_countries();
        
        foreach ($countries as $code => $country_data) {
            $patterns[$code] = self::get_phone_pattern($code);
        }
        
        return $patterns;
    }
}
